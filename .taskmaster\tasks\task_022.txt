# Task ID: 22
# Title: Update Entity Relationships and Joins
# Status: pending
# Dependencies: 2, 3, 4, 7, 8, 10, 11
# Priority: high
# Description: Review and update all entity relationships to include tenantId filtering in JOIN operations
# Details:
Update JOIN operations:
- Review all @ManyToOne, @OneToMany, @ManyToMany relationships
- Add tenantId filtering to JOIN queries
- Update QueryBuilder joins with tenantId conditions
- Handle cascade operations with tenant boundaries
- Ensure foreign key relationships respect tenant isolation
- Update eager loading with tenantId filtering

# Test Strategy:
Integration tests to verify relationship queries include tenantId
