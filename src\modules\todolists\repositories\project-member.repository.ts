import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProjectMember } from '../entities/project-members.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { QueryDto } from '@/common/dto/query.dto';
import { ProjectMemberRole } from '../enum/project-member-role.enum';

/**
 * Repository cho entity ProjectMember
 */
@Injectable()
export class ProjectMemberRepository {
  constructor(
    @InjectRepository(ProjectMember)
    private readonly repository: Repository<ProjectMember>,
  ) {}

  /**
   * Tạo thành viên dự án mới
   * @param data Dữ liệu thành viên dự án
   * @returns Thành viên dự án đã tạo
   */
  async create(data: Partial<ProjectMember>): Promise<ProjectMember> {
    const projectMember = this.repository.create(data);
    return this.repository.save(projectMember);
  }

  /**
   * Tìm tất cả thành viên của dự án với phân trang
   * @param projectId ID dự án
   * @param query Tham số truy vấn
   * @returns Danh sách thành viên dự án đã phân trang
   */
  async findAllByProjectId(
    projectId: number,
    query: QueryDto,
  ): Promise<PaginatedResult<ProjectMember>> {
    const {
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('projectMember');
    queryBuilder.where('projectMember.projectId = :projectId', { projectId });

    // Áp dụng sắp xếp
    queryBuilder.orderBy(`projectMember.${sortBy}`, sortDirection);

    // Áp dụng phân trang
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tìm thành viên dự án theo ID
   * @param id ID thành viên dự án
   * @returns Thành viên dự án hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<ProjectMember | null> {
    return this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Tìm thành viên dự án theo ID dự án và ID người dùng
   * @param projectId ID dự án
   * @param userId ID người dùng
   * @returns Thành viên dự án hoặc null nếu không tìm thấy
   */
  async findByProjectIdAndUserId(
    projectId: number,
    userId: number,
  ): Promise<ProjectMember | null> {
    return this.repository.findOne({
      where: {
        projectId,
        userId,
      },
    });
  }

  /**
   * Kiểm tra người dùng có phải là thành viên của dự án không
   * @param projectId ID dự án
   * @param userId ID người dùng
   * @returns true nếu là thành viên, false nếu không phải
   */
  async isProjectMember(projectId: number, userId: number): Promise<boolean> {
    const count = await this.repository.count({
      where: {
        projectId,
        userId,
      },
    });
    return count > 0;
  }

  /**
   * Kiểm tra người dùng có phải là admin của dự án không
   * @param projectId ID dự án
   * @param userId ID người dùng
   * @returns true nếu là admin, false nếu không phải
   */
  async isProjectAdmin(projectId: number, userId: number): Promise<boolean> {
    const count = await this.repository.count({
      where: {
        projectId,
        userId,
        role: ProjectMemberRole.ADMIN,
      },
    });
    return count > 0;
  }

  /**
   * Cập nhật thành viên dự án
   * @param id ID thành viên dự án
   * @param data Dữ liệu cập nhật
   * @returns Thành viên dự án đã cập nhật
   */
  async update(
    id: number,
    data: Partial<ProjectMember>,
  ): Promise<ProjectMember | null> {
    await this.repository.update(id, data);
    return this.findById(id);
  }

  /**
   * Xóa thành viên dự án
   * @param id ID thành viên dự án
   * @returns Kết quả xóa
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return (
      result.affected !== null &&
      result.affected !== undefined &&
      result.affected > 0
    );
  }

  /**
   * Xóa thành viên dự án theo ID dự án và ID người dùng
   * @param projectId ID dự án
   * @param userId ID người dùng
   * @returns Kết quả xóa
   */
  async deleteByProjectIdAndUserId(
    projectId: number,
    userId: number,
  ): Promise<boolean> {
    const result = await this.repository.delete({
      projectId,
      userId,
    });
    return (
      result.affected !== null &&
      result.affected !== undefined &&
      result.affected > 0
    );
  }
}
