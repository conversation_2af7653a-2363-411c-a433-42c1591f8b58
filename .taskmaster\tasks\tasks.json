{"tasks": [{"id": 1, "title": "Audit Current Codebase for TenantId Usage", "description": "Systematically review the entire codebase to identify all database queries, repositories, and services that need tenantId filtering", "status": "done", "priority": "high", "dependencies": [], "details": "Scan all modules (OKRs, Todolists, HRM, Calendar, Chat, System, Email) to identify:\n- Repository methods without tenantId filtering\n- Service methods that don't pass tenantId\n- Controller endpoints missing @CurrentTenant()\n- Raw SQL queries without tenantId\n- QueryBuilder calls without tenantId conditions", "testStrategy": "Create a checklist of all files that need updates and verify completeness", "createdAt": "2025-01-02T23:40:00.000Z"}, {"id": 2, "title": "Update OKRs Module - Objective Repository", "description": "Add tenantId filtering to all Objective repository methods", "status": "done", "priority": "high", "dependencies": [1], "details": "Update ObjectiveRepository methods:\n- findAll(): Add tenantId to QueryBuilder WHERE clause\n- findById(): Add tenantId to WHERE condition\n- create(): Ensure tenantId is set on new entities\n- update(): Add tenantId to WHERE condition\n- delete(): Add tenantId to WHERE condition\n- findByCycleId(): Add tenantId filtering\n- findByOwnerId(): Add tenantId filtering\n- findByParentId(): Add tenantId filtering\n- updateProgress(): Add tenantId to WHERE condition", "testStrategy": "Unit tests to verify tenantId is included in all queries and cross-tenant access is prevented", "createdAt": "2025-01-02T23:40:00.000Z"}, {"id": 3, "title": "Update OKRs Module - OkrCycle Repository", "description": "Add tenantId filtering to all OkrCycle repository methods", "status": "done", "priority": "high", "dependencies": [1], "details": "Update OkrCycleRepository methods:\n- findAll(): Add tenantId to QueryBuilder\n- findById(): Add tenantId to WHERE condition\n- findActive(): Add tenantId filtering\n- create(): Set tenantId on new entities\n- update(): Add tenantId to WHERE condition\n- delete(): Add tenantId to WHERE condition", "testStrategy": "Unit tests for repository methods with tenantId validation", "createdAt": "2025-01-02T23:40:00.000Z"}, {"id": 4, "title": "Update OKRs Module - KeyResult Repository", "description": "Add tenantId filtering to all KeyResult repository methods", "status": "cancelled", "priority": "high", "dependencies": [1], "details": "Update KeyResultRepository methods:\n- findAll(): Add tenantId filtering\n- findById(): Add tenantId to WHERE condition\n- findByObjectiveId(): Add tenantId filtering\n- create(): Set tenantId on new entities\n- update(): Add tenantId to WHERE condition\n- delete(): Add tenantId to WHERE condition\n- updateProgress(): Add tenantId filtering", "testStrategy": "Unit tests to ensure KeyResults are isolated by tenant", "createdAt": "2025-01-02T23:40:00.000Z"}, {"id": 5, "title": "Update OKRs Module - Services Layer", "description": "Update OKRs services to pass tenantId to repository methods", "status": "done", "priority": "high", "dependencies": [2, 3, 4], "details": "Update service classes:\n- ObjectiveService: Add tenantId parameter to all methods\n- OkrCycleService: Update to pass tenantId to repository\n- KeyResultService: Add tenantId handling\n- Remove @WithTenant decorators and use explicit tenantId passing\n- Update business logic to validate tenantId consistency", "testStrategy": "Integration tests to verify service layer properly handles tenantId", "createdAt": "2025-01-02T23:40:00.000Z"}, {"id": 6, "title": "Update OKRs Module - Controllers Layer", "description": "Update OKRs controllers to use @CurrentTenant() and pass tenantId to services", "status": "done", "priority": "high", "dependencies": [5], "details": "Update controller classes:\n- ObjectiveController: Add @CurrentTenant() to all endpoints\n- OkrCycleController: Update to extract and pass tenantId\n- KeyResultController: Add tenantId handling\n- Update API documentation to reflect tenantId requirements\n- Ensure all endpoints properly validate tenant access", "testStrategy": "API tests to verify tenant isolation at controller level", "createdAt": "2025-01-02T23:40:00.000Z"}, {"id": 7, "title": "Update Todo<PERSON> <PERSON><PERSON><PERSON> - <PERSON><PERSON> Repository", "description": "Add tenantId filtering to all Todo repository methods", "status": "done", "priority": "high", "dependencies": [1], "details": "Update TodoRepository methods:\n- findAll(): Add tenantId to QueryBuilder WHERE clause\n- findById(): Add tenantId to WHERE condition\n- findByProjectId(): Add tenantId filtering\n- findByAssigneeId(): Add tenantId filtering\n- create(): Set tenantId on new entities\n- update(): Add tenantId to WHERE condition\n- delete(): Add tenantId to WHERE condition\n- search(): Add tenantId to search queries", "testStrategy": "Unit tests to verify Todo isolation by tenant", "createdAt": "2025-01-02T23:40:00.000Z"}, {"id": 8, "title": "Update Todolists <PERSON><PERSON><PERSON> - Project Repository", "description": "Add tenantId filtering to all Project repository methods", "status": "done", "priority": "high", "dependencies": [1], "details": "Update ProjectRepository methods:\n- findAll(): Add tenantId filtering\n- findById(): Add tenantId to WHERE condition\n- findByOwnerId(): Add tenantId filtering\n- create(): Set tenantId on new entities\n- update(): Add tenantId to WHERE condition\n- delete(): Add tenantId to WHERE condition\n- getProjectStats(): Add tenantId to aggregation queries", "testStrategy": "Unit tests for Project repository with tenantId validation", "createdAt": "2025-01-02T23:40:00.000Z"}, {"id": 9, "title": "Update Todolists Module - Services and Controllers", "description": "Update Todolists services and controllers for tenantId handling", "status": "done", "priority": "high", "dependencies": [7, 8], "details": "Update service and controller layers:\n- TodoService: Add tenantId parameter to all methods\n- ProjectService: Update to pass tenantId to repository\n- TodoController: Add @CurrentTenant() to all endpoints\n- ProjectController: Update to extract and pass tenantId\n- Update business logic for tenant-aware operations", "testStrategy": "Integration and API tests for Todolists module", "createdAt": "2025-01-02T23:40:00.000Z"}, {"id": 10, "title": "Update HRM Module - Employee Repository", "description": "Add tenantId filtering to all Employee repository methods", "status": "done", "priority": "high", "dependencies": [1], "details": "Update EmployeeRepository methods:\n- findAll(): Add tenantId to QueryBuilder\n- findById(): Add tenantId to WHERE condition\n- findByDepartmentId(): Add tenantId filtering\n- findByPositionId(): Add tenantId filtering\n- create(): Set tenantId on new entities\n- update(): Add tenantId to WHERE condition\n- delete(): Add tenantId to WHERE condition\n- searchEmployees(): Add tenantId to search queries", "testStrategy": "Unit tests to ensure Employee data isolation", "createdAt": "2025-01-02T23:40:00.000Z"}, {"id": 11, "title": "Update HRM Module - Department and Position Repositories", "description": "Add tenantId filtering to Department and Position repository methods", "status": "done", "priority": "high", "dependencies": [1], "details": "Update repository methods:\n- DepartmentRepository: Add tenantId to all queries\n- PositionRepository: Add tenantId filtering\n- Handle hierarchical relationships with tenantId\n- Update organizational chart queries\n- Ensure department-employee relationships respect tenant boundaries", "testStrategy": "Unit tests for Department and Position repositories", "createdAt": "2025-01-02T23:40:00.000Z"}, {"id": 12, "title": "Update HRM Module - Services and Controllers", "description": "Update HRM services and controllers for tenantId handling", "status": "done", "priority": "high", "dependencies": [10, 11], "details": "Update service and controller layers:\n- EmployeeService: Add tenantId parameter to all methods\n- DepartmentService: Update to pass tenantId to repository\n- PositionService: Add tenantId handling\n- HRM Controllers: Add @CurrentTenant() to all endpoints\n- Update organizational reporting with tenantId filtering", "testStrategy": "Integration and API tests for HRM module", "createdAt": "2025-01-02T23:40:00.000Z"}, {"id": 13, "title": "Update Calendar Module - Event Repository", "description": "Add tenantId filtering to all Calendar Event repository methods", "status": "done", "priority": "medium", "dependencies": [1], "details": "Update EventRepository methods:\n- findAll(): Add tenantId to QueryBuilder\n- findById(): Add tenantId to WHERE condition\n- findByDateRange(): Add tenantId filtering\n- findByAttendeeId(): Add tenantId filtering\n- create(): Set tenantId on new entities\n- update(): Add tenantId to WHERE condition\n- delete(): Add tenantId to WHERE condition\n- getCalendarView(): Add tenantId to calendar queries", "testStrategy": "Unit tests for Calendar Event repository", "createdAt": "2025-01-02T23:40:00.000Z"}, {"id": 14, "title": "Update Calendar Module - Services and Controllers", "description": "Update Calendar services and controllers for tenantId handling", "status": "done", "priority": "medium", "dependencies": [13], "details": "Update service and controller layers:\n- EventService: Add tenantId parameter to all methods\n- CalendarService: Update to pass tenantId to repository\n- EventController: Add @CurrentTenant() to all endpoints\n- Update calendar view generation with tenantId filtering\n- Ensure event sharing respects tenant boundaries", "testStrategy": "Integration and API tests for Calendar module", "createdAt": "2025-01-02T23:40:00.000Z"}, {"id": 15, "title": "Update <PERSON><PERSON> - Message and Conversation Repositories", "description": "Add tenantId filtering to Chat module repositories", "status": "done", "priority": "medium", "dependencies": [1], "details": "Update repository methods:\n- MessageRepository: Add tenantId to all queries\n- ConversationRepository: Add tenantId filtering\n- Handle message history with tenantId\n- Update chat search with tenantId filtering\n- Ensure conversation participants are from same tenant", "testStrategy": "Unit tests for Chat repositories with tenantId validation", "createdAt": "2025-01-02T23:40:00.000Z"}, {"id": 16, "title": "Update <PERSON><PERSON> - Services and Controllers", "description": "Update Chat services and controllers for tenantId handling", "status": "done", "priority": "medium", "dependencies": [15], "details": "Update service and controller layers:\n- MessageService: Add tenantId parameter to all methods\n- ConversationService: Update to pass tenantId to repository\n- ChatController: Add @CurrentTenant() to all endpoints\n- Update real-time messaging with tenantId validation\n- Ensure WebSocket connections respect tenant boundaries", "testStrategy": "Integration and API tests for Chat module", "createdAt": "2025-01-02T23:40:00.000Z"}, {"id": 17, "title": "Update System Module - Notification Repository", "description": "Add tenantId filtering to System Notification repository methods", "status": "done", "priority": "medium", "dependencies": [1], "details": "Update NotificationRepository methods:\n- findAll(): Add tenantId to QueryBuilder\n- findById(): Add tenantId to WHERE condition\n- findByUserId(): Add tenantId filtering\n- markAsRead(): Add tenantId to WHERE condition\n- create(): Set tenantId on new entities\n- delete(): Add tenantId to WHERE condition\n- getUnreadCount(): Add tenantId to count queries", "testStrategy": "Unit tests for Notification repository", "createdAt": "2025-01-02T23:40:00.000Z"}, {"id": 18, "title": "Update System Module - Settings and Audit Repositories", "description": "Add tenantId filtering to System Settings and Audit repositories", "status": "done", "priority": "medium", "dependencies": [1], "details": "Update repository methods:\n- SettingsRepository: Add tenantId to all queries\n- AuditLogRepository: Add tenantId filtering\n- Handle system-wide vs tenant-specific settings\n- Update audit trail queries with tenantId\n- Ensure configuration changes are tenant-isolated", "testStrategy": "Unit tests for System repositories", "createdAt": "2025-01-02T23:40:00.000Z"}, {"id": 19, "title": "Update System Module - Services and Controllers", "description": "Update System services and controllers for tenantId handling", "status": "done", "priority": "medium", "dependencies": [17, 18], "details": "Update service and controller layers:\n- NotificationService: Add tenantId parameter to all methods\n- SettingsService: Update to pass tenantId to repository\n- AuditService: Add tenantId handling\n- System Controllers: Add @CurrentTenant() where appropriate\n- Update system reporting with tenantId filtering", "testStrategy": "Integration and API tests for System module", "createdAt": "2025-01-02T23:40:00.000Z"}, {"id": 20, "title": "Update <PERSON><PERSON> - Template and Log Repositories", "description": "Add tenantId filtering to Email module repositories", "status": "done", "priority": "low", "dependencies": [1], "details": "Update repository methods:\n- EmailTemplateRepository: Add tenantId to all queries\n- EmailLogRepository: Add tenantId filtering\n- Handle tenant-specific email templates\n- Update email history queries with tenantId\n- Ensure email configurations are tenant-isolated", "testStrategy": "Unit tests for Email repositories", "createdAt": "2025-01-02T23:40:00.000Z"}, {"id": 21, "title": "Update Email Module - Services and Controllers", "description": "Update Email services and controllers for tenantId handling", "status": "done", "priority": "low", "dependencies": [20], "details": "Update service and controller layers:\n- EmailService: Add tenantId parameter to all methods\n- EmailTemplateService: Update to pass tenantId to repository\n- Email Controllers: Add @CurrentTenant() where appropriate\n- Update email sending with tenant-specific templates\n- Ensure email logs are tenant-isolated", "testStrategy": "Integration and API tests for Email module", "createdAt": "2025-01-02T23:40:00.000Z"}, {"id": 22, "title": "Update Entity Relationships and Joins", "description": "Review and update all entity relationships to include tenantId filtering in JOIN operations", "status": "in-progress", "priority": "high", "dependencies": [2, 3, 7, 8, 10, 11], "details": "Update JOIN operations:\n- Review all @ManyToOne, @OneToMany, @ManyToMany relationships\n- Add tenantId filtering to JOIN queries\n- Update QueryBuilder joins with tenantId conditions\n- Handle cascade operations with tenant boundaries\n- Ensure foreign key relationships respect tenant isolation\n- Update eager loading with tenantId filtering", "testStrategy": "Integration tests to verify relationship queries include tenantId", "createdAt": "2025-01-02T23:40:00.000Z"}, {"id": 23, "title": "Create Comprehensive Test Suite", "description": "Develop comprehensive tests to verify tenant isolation across all modules", "status": "pending", "priority": "high", "dependencies": [6, 9, 12, 14, 16, 19, 21, 22], "details": "Create test suites:\n- Unit tests for all repository methods with tenantId\n- Integration tests for service layer tenant handling\n- API tests for controller endpoints with @CurrentTenant()\n- Security tests to verify cross-tenant access prevention\n- Performance tests with tenantId indexes\n- End-to-end tests for complete user workflows", "testStrategy": "Automated test suite with 100% coverage of tenant-related functionality", "createdAt": "2025-01-02T23:40:00.000Z"}, {"id": 24, "title": "Performance Optimization and Indexing", "description": "Optimize database performance for tenantId filtering and create appropriate indexes", "status": "pending", "priority": "medium", "dependencies": [22, 23], "details": "Performance optimization:\n- Create database indexes on tenantId columns\n- Optimize QueryBuilder queries with tenantId\n- Review and optimize JOIN operations\n- Implement query result caching with tenant awareness\n- Monitor query performance with tenantId filtering\n- Create composite indexes for common query patterns", "testStrategy": "Performance benchmarks before and after optimization", "createdAt": "2025-01-02T23:40:00.000Z"}, {"id": 25, "title": "Documentation and Code Review", "description": "Update documentation and conduct thorough code review for tenant isolation implementation", "status": "pending", "priority": "medium", "dependencies": [23, 24], "details": "Documentation and review:\n- Update API documentation with tenantId requirements\n- Create developer guidelines for tenant-aware development\n- Document best practices for tenantId handling\n- Conduct comprehensive code review\n- Update deployment and migration guides\n- Create troubleshooting guide for tenant issues", "testStrategy": "Documentation review and validation of all guidelines", "createdAt": "2025-01-02T23:40:00.000Z"}]}