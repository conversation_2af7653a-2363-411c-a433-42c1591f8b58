import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TodoTag } from '../entities/todo-tag.entity';

/**
 * Repository cho entity TodoTag
 */
@Injectable()
export class TodoTagRepository {
  constructor(
    @InjectRepository(TodoTag)
    private readonly repository: Repository<TodoTag>,
  ) {}

  /**
   * Tạo liên kết giữa todo và label
   * @param data Dữ liệu liên kết
   * @returns Liên kết đã tạo
   */
  async create(data: Partial<TodoTag>): Promise<TodoTag> {
    const todoTag = this.repository.create(data);
    return this.repository.save(todoTag);
  }

  /**
   * Tìm tất cả tag của một todo
   * @param todoId ID của todo
   * @returns Danh sách tag
   */
  async findByTodoId(todoId: number): Promise<TodoTag[]> {
    return this.repository.find({
      where: { todoId },
    });
  }

  /**
   * Tìm tag theo ID
   * @param id ID của tag
   * @returns Tag hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<TodoTag | null> {
    return this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Tìm tag theo todoId và labelsId
   * @param todoId ID của todo
   * @param labelsId ID của label
   * @returns Tag hoặc null nếu không tìm thấy
   */
  async findByTodoIdAndLabelsId(
    todoId: number,
    labelsId: number,
  ): Promise<TodoTag | null> {
    return this.repository.findOne({
      where: {
        todoId,
        labelsId,
      },
    });
  }

  /**
   * Xóa tag
   * @param id ID của tag
   * @returns true nếu xóa thành công
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return (
      result.affected !== null &&
      result.affected !== undefined &&
      result.affected > 0
    );
  }

  /**
   * Xóa tag theo todoId và labelsId
   * @param todoId ID của todo
   * @param labelsId ID của label
   * @returns true nếu xóa thành công
   */
  async deleteByTodoIdAndLabelsId(
    todoId: number,
    labelsId: number,
  ): Promise<boolean> {
    const result = await this.repository.delete({
      todoId,
      labelsId,
    });
    return (
      result.affected !== null &&
      result.affected !== undefined &&
      result.affected > 0
    );
  }
}
