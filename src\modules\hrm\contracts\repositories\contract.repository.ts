import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  Repository,
  Between,
  LessThanOrEqual,
  MoreThanOrEqual,
  In,
} from 'typeorm';
import { Contract } from '../entities/contract.entity';
import { ContractQueryDto } from '../dto/contract-query.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { ContractStatus } from '../enum/contract-status.enum';

/**
 * Repository for contract entity
 */
@Injectable()
export class ContractRepository {
  private readonly logger = new Logger(ContractRepository.name);

  constructor(
    @InjectRepository(Contract)
    private readonly repository: Repository<Contract>,
  ) {}

  /**
   * Find all contracts with pagination and filtering
   * @param query Query parameters
   * @returns Paginated list of contracts
   */
  async findAll(query: ContractQueryDto): Promise<PaginatedResult<Contract>> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'contractCode',
      sortDirection = 'ASC',
      employeeId,
      status,
      contractType,
      startDateFrom,
      startDateTo,
      endDateFrom,
      endDateTo,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('contract');

    // Apply filters if provided
    if (employeeId) {
      queryBuilder.andWhere('contract.employeeId = :employeeId', {
        employeeId,
      });
    }

    if (status) {
      queryBuilder.andWhere('contract.status = :status', { status });
    }

    if (contractType) {
      queryBuilder.andWhere('contract.contractType = :contractType', {
        contractType,
      });
    }

    // Date range filters
    if (startDateFrom && startDateTo) {
      queryBuilder.andWhere(
        'contract.startDate BETWEEN :startDateFrom AND :startDateTo',
        {
          startDateFrom,
          startDateTo,
        },
      );
    } else if (startDateFrom) {
      queryBuilder.andWhere('contract.startDate >= :startDateFrom', {
        startDateFrom,
      });
    } else if (startDateTo) {
      queryBuilder.andWhere('contract.startDate <= :startDateTo', {
        startDateTo,
      });
    }

    if (endDateFrom && endDateTo) {
      queryBuilder.andWhere(
        'contract.endDate BETWEEN :endDateFrom AND :endDateTo',
        {
          endDateFrom,
          endDateTo,
        },
      );
    } else if (endDateFrom) {
      queryBuilder.andWhere('contract.endDate >= :endDateFrom', {
        endDateFrom,
      });
    } else if (endDateTo) {
      queryBuilder.andWhere('contract.endDate <= :endDateTo', { endDateTo });
    }

    // Apply search filter if provided
    if (search) {
      queryBuilder.andWhere(
        'contract.contractCode ILIKE :search OR contract.title ILIKE :search',
        { search: `%${search}%` },
      );
    }

    // Apply sorting
    queryBuilder.orderBy(`contract.${sortBy}`, sortDirection);

    // Apply pagination
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Find contract by ID
   * @param id Contract ID
   * @returns Contract or null if not found
   */
  async findById(id: number): Promise<Contract | null> {
    return this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Find contract by contract code
   * @param contractCode Contract code
   * @returns Contract or null if not found
   */
  async findByContractCode(contractCode: string): Promise<Contract | null> {
    return this.repository.findOne({
      where: { contractCode },
    });
  }

  /**
   * Find contracts by employee ID
   * @param employeeId Employee ID
   * @returns List of contracts
   */
  async findByEmployeeId(employeeId: number): Promise<Contract[]> {
    return this.repository.find({
      where: { employeeId },
      order: { startDate: 'DESC' },
    });
  }

  /**
   * Find active contract by employee ID
   * @param employeeId Employee ID
   * @returns Active contract or null if not found
   */
  async findActiveContractByEmployeeId(
    employeeId: number,
  ): Promise<Contract | null> {
    const now = new Date();
    return this.repository.findOne({
      where: {
        employeeId,
        status: ContractStatus.ACTIVE,
        startDate: LessThanOrEqual(now),
        endDate: MoreThanOrEqual(now),
      },
    });
  }

  /**
   * Find contracts expiring soon
   * @param daysThreshold Number of days to consider "soon"
   * @returns List of contracts expiring soon
   */
  async findContractsExpiringSoon(daysThreshold: number): Promise<Contract[]> {
    const today = new Date();
    const thresholdDate = new Date();
    thresholdDate.setDate(today.getDate() + daysThreshold);

    return this.repository.find({
      where: {
        status: ContractStatus.ACTIVE,
        endDate: Between(today, thresholdDate),
      },
      order: { endDate: 'ASC' },
    });
  }

  /**
   * Create a new contract
   * @param data Contract data
   * @returns Created contract
   */
  async create(data: Partial<Contract>): Promise<Contract> {
    const contract = this.repository.create(data);
    return this.repository.save(contract);
  }

  /**
   * Update contract
   * @param id Contract ID
   * @param data Updated contract data
   * @returns Updated contract or null if not found
   */
  async update(id: number, data: Partial<Contract>): Promise<Contract | null> {
    await this.repository.update({ id }, data);
    return this.findById(id);
  }

  /**
   * Delete contract
   * @param id Contract ID
   * @returns True if deleted, false if not found
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete({ id });
    return (result.affected ?? 0) > 0;
  }

  /**
   * Update contract status
   * @param id Contract ID
   * @param status New status
   * @returns Updated contract or null if not found
   */
  async updateStatus(
    id: number,
    status: ContractStatus,
  ): Promise<Contract | null> {
    await this.repository.update({ id }, { status });
    return this.findById(id);
  }

  /**
   * Terminate contract
   * @param id Contract ID
   * @param terminationDate Termination date
   * @param terminationReason Termination reason
   * @returns Updated contract or null if not found
   */
  async terminateContract(
    id: number,
    terminationDate: Date,
    terminationReason: string,
  ): Promise<Contract | null> {
    await this.repository.update(
      { id },
      {
        status: ContractStatus.TERMINATED,
        terminationDate,
        terminationReason,
      },
    );
    return this.findById(id);
  }
}
